import { useState } from 'react';

export const usePopupDialog = () => {
  const [popupId, setPopupId] = useState<string | null>(null);

  const openDialog = (id: string) => {
    setPopupId(id);
  };

  const closeDialog = () => {
    setPopupId(null);
  };

  const handleConfirm = (inputValue?: string) => {
    if (popupId === 'CancelOrder' && inputValue) {
      console.log('Cancellation reason:', inputValue);
    } else {
      console.log('Confirmed action for:', popupId);
    }
    closeDialog();
  };

  return {
    popupId,
    openDialog,
    closeDialog,
    handleConfirm,
    isOpen: !!popupId
  };
};