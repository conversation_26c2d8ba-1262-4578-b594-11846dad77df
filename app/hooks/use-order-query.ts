import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useOrderQuery({ currentPage }: { currentPage: number }) {
  const apiFn = (): Promise<unknown> => {
    return api.get(API_ENDPOINT.ORDERS, {
      params: {
        page: currentPage + 1
        // limit: CONSTANTS.LIMIT_PER_PAGE
      }
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.ORDERS, currentPage],
    queryFn: apiFn
  });

  return query;
}
