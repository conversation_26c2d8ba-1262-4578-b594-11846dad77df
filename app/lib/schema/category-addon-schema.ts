import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

export const categoryAddonSchema = z.object({
  id: z.string().optional(),
  photo: z.string().url('Must be a valid URL for the photo.').or(z.literal('')).optional(),
  name: multiLangNameSchema,
  price: z.string().min(1, 'Price is required.')
});

export const dynamicCategoryAddonFormSchema = z.object({
  // The 'items' array must contain at least one item
  categoryAddon: categoryAddonSchema,
  categoryAddonVariants: z
    .array(categoryAddonSchema)
    .min(1, { message: 'You must have at least one product item.' })
});

export type CategoryAddonSchemaProps = z.infer<typeof categoryAddonSchema>;
export type DynamicCategoryAddonSchemaProps = z.infer<typeof dynamicCategoryAddonFormSchema>;
