import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

export const productSchema = z.object({
  name: multiLangNameSchema,
  price: z.string().min(1, 'Price is required'),
  status: z.enum(['Active', 'Inactive']),
  categories: z.array(z.string()).min(1, { message: 'You must have at least one product item.' }),
  attachments: z.array(z.string())
});

export type ProductSchemaProps = z.infer<typeof productSchema>;
