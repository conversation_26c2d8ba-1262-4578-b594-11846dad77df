import OrderComponent from '@/components/common/order-component';
import {
  dummyOrderCompleteService,
  dummyOrders,
  orderDetails,
  PaginationDummy,
  serviceDetailsPopupDummy
} from '@/constants/data-dummy';
import { serviceDetailsDummy } from '@/constants/data-dummy';
import ServiceDetails from '@/components/common/Service-details-component';
import { useState } from 'react';
import OrderAssignCleaner from '@/components/common/order-assign-cleaner';
import OrderDetailCard from '@/components/common/order-detail-card';
import OrderHeader from '@/components/common/order-detail-header';
import CategorySearchInterface from '@/components/category/Category-search-interface';
import PaginationDemo from '@/components/common/Pagination-component';
import ServicePopup from '@/components/common/service-detail-popup-form';
import { OrderCompleteServiceData } from '@/components/common/order-complete-service-card';
// import useOrderQuery from '@/hooks/use-order-query';

export default function OrdersList() {
  // const { data, isFetching } = useOrderQuery({ currentPage: 0 });
  const [selectedOrderId, setSelectedOrderId] = useState<string | undefined>(undefined);

  // Make formData stateful for ServicePopup
  const [formData, setFormData] = useState(serviceDetailsPopupDummy.formData);
  // Control popup open/close (optional, here always open for demo)
  const [isPopupOpen, setIsPopupOpen] = useState(serviceDetailsPopupDummy.isOpen);

  const handleOrderClick = (orderId?: string) => {
    setSelectedOrderId(orderId);
  };

  // const { popupId, openDialog, closeDialog, handleConfirm, isOpen } = usePopupDialog();

  const selectedOrderDetail = selectedOrderId ? orderDetails[selectedOrderId] : null;
  console.log('Selected order detail:', selectedOrderDetail);

  // Handler for ServicePopup form changes
  const handleFormChange = (newFormData: typeof formData) => {
    setFormData(newFormData);
  };

  return (
    <>
      {selectedOrderDetail && (
        <OrderHeader orderId={selectedOrderDetail.orderId} status={selectedOrderDetail.status} />
      )}

      <div className="flex flex-row h-full">
        <div className="overflow-y-auto p-4">
          <CategorySearchInterface />
          {dummyOrders.map((order, index) => (
            <OrderComponent
              key={index}
              {...order}
              isActive={selectedOrderId === order.id}
              onClick={handleOrderClick}
            />
          ))}
          <PaginationDemo
            currentPage={PaginationDummy[0].currentPage}
            totalPages={PaginationDummy[0].totalPages}
          />
        </div>
        <div className="overflow-auto flex flex-1 bg-muted p-4 flex-col">
          {selectedOrderDetail ? <OrderDetailCard /> : <div>Select an order to see details</div>}
          <div className="flex flex-row mt-6">
            <div className="flex-1">
              <div className="flex flex-col gap-4">
                <ServiceDetails {...serviceDetailsDummy} />
                <ServiceDetails {...serviceDetailsDummy} />
              </div>
            </div>
            <div>
              <OrderAssignCleaner />
              {/* The popup dialog */}
              {/* {isOpen && (
        <PopUpUI
          popupId={popupId!}
          open={isOpen}
          onClose={closeDialog}
          onConfirm={handleConfirm}
        />
      )} */}

              {dummyOrderCompleteService.map((order) => (
                <OrderCompleteServiceData key={order.id} {...order} />
              ))}
            </div>
          </div>
        </div>
      </div>
      <ServicePopup
        id={serviceDetailsPopupDummy.id}
        serviceTitle={serviceDetailsPopupDummy.serviceTitle}
        isOpen={isPopupOpen}
        formData={formData}
        products={serviceDetailsPopupDummy.products}
        variants={serviceDetailsPopupDummy.variants}
        quantities={serviceDetailsPopupDummy.quantities}
        paymentStatuses={serviceDetailsPopupDummy.paymentStatuses}
        paymentMethods={serviceDetailsPopupDummy.paymentMethods}
        onClose={() => setIsPopupOpen(false)}
        onChange={handleFormChange}
      />
    </>
  );
}
