import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SearchBar from '../common/search-bar';
import { FilterHorizontalIcon } from 'hugeicons-react';

const OrderSearchInterface: React.FC = () => {
  const [searchValue, setSearchValue] = useState('');

  return (
    <div className="flex gap-[16px] p-4 max-w-md">
      {/* Search Bar */}
      <div className="flex-1">
        <SearchBar
          placeholder="Search for order id..."
          value={searchValue}
          onChange={setSearchValue}
        />
      </div>
      {/* Filter Button */}
      <div className="flex gap-[8px]">
        <Button
          variant="outline"
          size="icon"
          className="h-[36px] w-[36px] border border-gray-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
        >
          <FilterHorizontalIcon size={24} color="#000000" strokeWidth={1.5} />
        </Button>
        {/* Add Button */}
        <Button
          size="icon"
          className="h-[36px] w-[36px] bg-[#1964AD] hover:bg-blue-700 text-white  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default OrderSearchInterface;
