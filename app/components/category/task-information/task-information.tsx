import CustomTabs from '@/components/common/custom-tabs';
import DraggableInputBoxPanel from '@/components/common/draggable/draggable-inputbox-panel';
import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TaskInformationPanel from './task-information-panel';
import type { Control, Path } from 'react-hook-form';
import type { CategorySchemaProps } from '@/lib/schema/category-schema';

const languages = [
  { label: 'English', value: 'en' },
  { label: 'Khmer', value: 'km' },
  { label: 'Vietnamese', value: 'vn' },
  { label: 'Chinese (Traditional)', value: 'zh' },
  { label: 'Chinese (Simplified)', value: 'cn' }
];

type Props = {
  control: Control<CategorySchemaProps>;
  name: Path<CategorySchemaProps>;
  title: string;
};

export function TaskInformation({ control, name, title }: Props) {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const [products, setProducts] = useState<DraggableInputProps[]>([]);

  return (
    <Dialog open={open}>
      <DialogTrigger asChild>
        <TaskInformationPanel
          title={title}
          control={control}
          name={name}
          onClick={() => setOpen(true)}
        />
      </DialogTrigger>
      <DialogContent className="p-0 w-[75vw] flex flex-col h-[70vh] overflow-hidden gap-0">
        <DialogHeader className="p-6  ">
          <DialogTitle>Edit profile</DialogTitle>
        </DialogHeader>

        <CustomTabs tabs={languages}>
          {(tab) => (
            <div>
              <Input placeholder={tab.label} />
              <DraggableInputBoxPanel
                title={t('categoryPage.product')}
                buttonText={t('categoryPage.addProduct')}
                data={products}
                onChange={setProducts}
              />
            </div>
          )}
        </CustomTabs>

        <DialogFooter className="border-t p-6">
          <Button size="sm" type="submit">
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
