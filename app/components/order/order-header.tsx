import { useNavigate } from 'react-router';
import { ChevronLeft } from 'lucide-react';
import { Button } from '../ui/button';

export default function OrderHeader() {
  const navigate = useNavigate();
  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="h-[88px] p-6 pl-0 flex w-full items-center flex-row">
      <div className="flex flex-1 flex-row gap-4 items-center pl-4">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ChevronLeft className="!size-6" />
        </Button>

        <p className="text-xl font-bold">Order Management</p>
      </div>
    </div>
  );
}
