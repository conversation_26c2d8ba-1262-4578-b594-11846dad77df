import { type FieldValues, type Path, type UseFormReturn } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';
import { useState } from 'react';
import InputMultipleLangsDialog from './input-multiple-langs-dialog';

type Props<T extends FieldValues> = {
  form: UseFormReturn<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  showError?: boolean;
};

export default function FormInputMultipleLanguages<T extends FieldValues>({
  name,
  label,
  placeholder,
  form,
  showError
}: Props<T>) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const { control } = form;

  return (
    <FormField
      control={control}
      name={`${name}.en` as Path<T>}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Input
              placeholder={placeholder}
              value={field.value}
              readOnly
              className="cursor-pointer"
              onClick={() => setIsPopupOpen(true)}
            />
          </FormControl>
          {showError && <FormMessage />}
          <InputMultipleLangsDialog
            form={form}
            name={name}
            label={label}
            isPopupOpen={isPopupOpen}
            setIsPopupOpen={setIsPopupOpen}
          />
        </FormItem>
      )}
    />
  );
}
