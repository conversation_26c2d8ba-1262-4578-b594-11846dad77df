import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis
} from '@/components/ui/pagination'; // adjust the import path if needed\

interface PaginationDemoProps {
  currentPage: number;
  totalPages: number;
}

const PaginationDemo: React.FC<PaginationDemoProps> = ({
  currentPage: initialPage,
  totalPages
}) => {
  const [currentPage, setCurrentPage] = React.useState(initialPage);
  const visiblePages = 3;

  const getStartPage = () => {
    return Math.floor((currentPage - 1) / visiblePages) * visiblePages + 1;
  };
  const renderPageNumbers = () => {
    const pages = [];
    const startPage = getStartPage();
    const endPage = Math.min(startPage + visiblePages - 1, totalPages);
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink isActive={i === currentPage} onClick={() => setCurrentPage(i)}>
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    if (endPage < totalPages) {
      pages.push(
        <PaginationItem key="ellipsis">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    return pages;
  };
  return (
    <div className="p-8 bg-background min-h-screen">
      <div className=" mx-auto space-y-8">
        <Pagination className="justify-start">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
              />
            </PaginationItem>
            {renderPageNumbers()}
            <PaginationItem>
              <PaginationNext
                onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};
export default PaginationDemo;
