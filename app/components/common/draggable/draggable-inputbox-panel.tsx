import type { Dispatch, SetStateAction } from 'react';
import { useTranslation } from 'react-i18next';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import clsx from 'clsx';
import SortableInput from './sortable-input';

type Props = {
  title: string;
  buttonText?: string;
  data: DraggableInputProps[];
  onChange: Dispatch<SetStateAction<DraggableInputProps[]>>;
};

export default function DraggableInputBoxPanel({ title, buttonText, data, onChange }: Props) {
  const { t } = useTranslation();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      onChange((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const handleUpdateTask = (id: string, updates: Partial<DraggableInputProps>) => {
    onChange((prev) => prev.map((task) => (task.id === id ? { ...task, ...updates } : task)));
  };

  const handleDeleteTask = (id: string) => {
    onChange((prev) => prev.filter((task) => task.id !== id));
  };
  const handleAddTask = () => {
    const newTask: DraggableInputProps = {
      id: Date.now().toString(),
      value: ''
    };
    onChange((prev) => [...prev, newTask]);
  };

  return (
    <Card className="flex flex-col gap-0 shadow-none">
      <CardContent className="p-0">
        <div
          className={clsx({
            'pb-4': data.length > 0
          })}>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}>
            <SortableContext
              items={data.map((task) => task.id)}
              strategy={verticalListSortingStrategy}>
              <div className="space-y-4">
                {data.map((item) => (
                  <SortableInput
                    item={item}
                    placeholder={title}
                    key={item.id}
                    onChange={(value) => handleUpdateTask(item.id, { value })}
                    onDelete={handleDeleteTask}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      </CardContent>
      <div>
        <Button className="!pl-0" variant="link" onClick={handleAddTask}>
          <Plus />
          {buttonText || t('addAnother')}
        </Button>
      </div>
    </Card>
  );
}
