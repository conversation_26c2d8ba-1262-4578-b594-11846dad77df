type Props = {
  name: string;
  phone_number: string;
  img: string;
  status: 'pending' | 'paid' | 'unpaid';
};

export default function OrderProfile({ name, phone_number, img, status }: Props) {
  const statusStyle = {
    paid: 'bg-[#E6F9F1] text-[#06C270]',
    pending: 'bg-[#FFF4E5] text-[#FFA726]',
    unpaid: 'bg-[#FDECEA] text-[#E53935]'
  }[status];

  return (
    <div className="flex items-center justify-between w-full">
      {/* Profile Info */}
      <div className="flex items-center gap-4 flex-1">
        <img src={img} alt="Profile" className="w-10 h-10 rounded-full object-cover" />
        <div className="text-sm">
          <h3 className="text-[#1A1A1A] font-inter text-base font-bold leading-6 capitalize">
            {name}
          </h3>
          <p className="text-gray-600">{phone_number}</p>
        </div>
      </div>

      {/* Status Badge */}
      <span className={`text-xs font-bold rounded-full px-4 py-1 ${statusStyle}`}>{status}</span>
    </div>
  );
}
