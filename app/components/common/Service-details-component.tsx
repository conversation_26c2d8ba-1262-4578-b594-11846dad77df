import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import ViewmorePopover from './viewmore-popover';
interface ServiceItem {
  id: string;
  name: string;
  checked: boolean;
}
interface ServiceDetailsProps {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  items: ServiceItem[];
  imageSrc: string;
}
const ServiceDetails: React.FC<ServiceDetailsProps> = ({
  id,
  serviceName,
  category,
  addOns,
  price,
  items,
  imageSrc
}) => {
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>(items);
  const toggleItem = (index: number) => {
    const newItems = [...serviceItems];
    newItems[index].checked = !newItems[index].checked;
    setServiceItems(newItems);
  };
  return (
    <Card className="max-w-2xl mx-auto bg-gray-50 w-[620px]">
      <CardHeader className="px-6">
        <div className="flex items-center justify-between">
          <h2 className="text-base font-bold text-gray-700">
            {serviceName} <span className="text-sm text-gray-400 ml-2">#{id}</span>
          </h2>
        </div>
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center">
              <img src={imageSrc} alt={category} className="w-[48px] h-[48px] rounded-full" />
            </div>
            <div>
              <span className="font-bold text-base text-[#1A1A1A]">{category}</span>
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({addOns} Service Add-On{addOns > 1 ? 's' : ''})
              </span>
            </div>
          </div>
          <span className="font-bold text-[16px] text-[#1A1A1A]">${price.toFixed(2)}</span>
        </div>
      </CardHeader>
      <CardContent>
        <h3 className="font-semibold text-base text-gray-900 mb-6">Service Details</h3>
        <div className="grid grid-cols-3 gap-y-6 gap-x-6">
          {serviceItems.map((item, index) => (
            <div key={item.name} className="flex items-center gap-3">
              <Checkbox
                id={`service-${item.id}`}
                checked={item.checked}
                onCheckedChange={() => toggleItem(index)}
                className="w-[20px] h-[20px] text-blue-600 border-gray-300"
              />
              <label
                htmlFor={`service-${item.id}`}
                className="text-[14px] font-medium text-[#1A1A1A] cursor-pointer"
              >
                {item.name}
              </label>
            </div>
          ))}
        </div>
        {serviceItems.length > 18 && (
          <div className="mt-4 flex justify-end">
            <ViewmorePopover />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
export default ServiceDetails;
