type Props = {
  date: string;
  duration: number;
};

export default function OrderDate({ date,duration }: Props) {
  return (
    <div className="space-y-1  gap-8  ">
      <div className="flex justify-between items-center">
        <p className="text-[#707070] font-medium text-sm font-inter">Date</p>
      </div>
      <p className="text-black font-medium text-sm font-inter">{date} ({duration}Hours)  </p>
       
    </div>
  );
}
