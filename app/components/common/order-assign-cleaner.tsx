
import { Button } from '../ui/button';
import OrderCleaner from './order-cleaner';
import OrderAddCleaner from './order-add-cleaner';
import PaymentInfo from './order-payment';
import { useState } from 'react';

export default function OrderAssignCleaner() {
  const [isEditing, setIsEditing] = useState(false);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleSaveClick = () => {
    setIsEditing(false);
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-6 p-6 border-b">
        <h4 className="text-lg font-bold text-gray-900">Assign Cleaner</h4>
        {isEditing ? (
          <Button variant="link" onClick={handleSaveClick}>
            Save
          </Button>
        ) : (
          <Button variant="link" onClick={handleEditClick}>
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <OrderAddCleaner serviceNumber={1} />
      ) : (
        <OrderCleaner serviceNumber={1} quantity={1} />
      )}

      <PaymentInfo />
    </div>
  );
}
