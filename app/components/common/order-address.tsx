import { MapsIcon } from 'hugeicons-react';

type Props = {
  address: string;
  lat: number;
  lng: number;
};

export default function OrderAddress({ address, lat, lng }: Props) {
  const openMap = () => {
    window.open(`https://www.google.com/maps/search/?api=1&query=${lat},${lng}`, '_blank');
  };

  return (
    <div className="space-y-1 max-w-[60%]">
      <div className="flex justify-between items-center">
        <p className="text-[#707070] font-medium text-sm font-inter">Address</p>
        <button
          onClick={openMap}
          className="flex items-center gap-1 text-sm text-primary hover:underline focus:outline-none"
        >
          View Map
          <MapsIcon size={18} strokeWidth={1.5} color="#1964AD" />
        </button>
      </div>
      <p className="text-black font-medium text-sm font-inter">{address}</p>
    </div>
  );
}