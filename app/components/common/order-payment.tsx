import React, { useState, useEffect } from 'react';
import PaymentLabel from './order-payment-label';

interface PaymentData {
  service: number;
  discount: number;
  serviceFee: number;
  transportFee: number;
  vatRate: number;
  paymentMethod: string;
  currency?: string;
}

interface PaymentInfoProps {
  data?: PaymentData;
}

const PaymentInfo: React.FC<PaymentInfoProps> = ({ data }) => {
  const [paymentData, setPaymentData] = useState<PaymentData>({
    service: 30.0,
    discount: 3.0,
    serviceFee: 3.0,
    transportFee: 5.0,
    vatRate: 10,
    paymentMethod: 'bEasy Points'
  });

  useEffect(() => {
    if (data) {
      setPaymentData((prev) => ({ ...prev, ...data }));
    }
  }, [data]);

  const calculateSubtotal = () => {
    return (
      paymentData.service + paymentData.serviceFee + paymentData.transportFee - paymentData.discount
    );
  };

  const calculateVAT = () => {
    return calculateSubtotal() * (paymentData.vatRate / 100);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateVAT();
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Payment Information</h1>
      <div className="space-y-4 border-t pt-4">
        {/* Service */}
        <PaymentLabel label="Service:" value={paymentData.service} />
        <PaymentLabel label="Discount:" value={paymentData.discount} />
        <PaymentLabel label="Service Fee:" value={paymentData.serviceFee} />
        <PaymentLabel label="Transport Fee:" value={paymentData.transportFee} />
        <PaymentLabel label="SubTotal:" value={calculateSubtotal()} />
        <PaymentLabel label="VAT" value={calculateVAT()} vatRate={paymentData.vatRate} />
        <PaymentLabel label="Payment Method:" value={paymentData.paymentMethod} />
        <div className=" border-t pt-4">
          <span className=" font-bold text-gray-900">
            {' '}
            <PaymentLabel label="Total:" value={calculateTotal()} />
          </span>
        </div>
      </div>

    </div>
  );
};

export default PaymentInfo;
