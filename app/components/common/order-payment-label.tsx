type Props = {
  label: string;
  value: number | string;
  formatCurrency?: (amount: number) => string;
  vatRate?: number;
  currency?: string;
};

export default function PaymentLabel({ label, value, formatCurrency, vatRate, currency }: Props) {
  const defaultFormatCurrency = (amount: number) => {
    const curr = currency || 'USD';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: curr
    }).format(amount);
  };

  const displayValue =
    typeof value === 'number' && (formatCurrency || defaultFormatCurrency)
      ? (formatCurrency || defaultFormatCurrency)(value)
      : value;

  return (
    <div className="flex justify-between items-center">
      <span className="text-gray-700">
        {label}
        {vatRate !== undefined ? ` (${vatRate}%):` : ''}
      </span>
      <span className="font-medium">{displayValue}</span>
    </div>
  );
}
