import React from 'react';
import OrderAddress from '../common/order-address';
import OrderDate from '../common/order-date';
import OrderProfile from '../common/order-profile';
type OrderStatus = "pending" | "paid" | "unpaid";
// Props definitions
type OrderProfileProps = {
  fullName: string;
  phoneNumber: string;
  img: string;
  status: OrderStatus;
};

type OrderAddressProps = {
  address: string;
  lat: number;
  lng: number;
};

type OrderDateProps = {
  date: string;
  duration: number;
};

type OrderDetailProps = {
  profileData: OrderProfileProps;
  addressData: OrderAddressProps;
  dateData: OrderDateProps;
};

// Reusable Order Detail Component
export function OrderDetail({ profileData, addressData, dateData }: OrderDetailProps) {
  return (
    <div className="w-[984px] p-6 bg-gray-50 space-y-6 rounded-[12px]">
      <div className="flex justify-between items-center mx-6">
        <p className="text-black text-xl font-bold">Detail</p>
      </div>

      <div className="flex items-start justify-between py-6 mx-6">
        <OrderProfile
          name={profileData.fullName}
          phone_number={profileData.phoneNumber}
          img={profileData.img}
          status={profileData.status}
        />
      </div>

      <div className="flex justify-between text-sm text-[#4B5563] mx-6">
        <OrderAddress
          address={addressData.address}
          lat={addressData.lat}
          lng={addressData.lng}
        />
        <OrderDate
          date={dateData.date}
          duration={dateData.duration}
        />
      </div>
    </div>
  );
}

// Parent component for demonstration
const ParentComponent: React.FC = () => {
  const profileData: OrderProfileProps = {
    fullName: 'Nypanith Reth',
    phoneNumber: '+855 9792334',
    img: 'app/asset/images/beasy-icon.png',
    status: 'paid',
  };

  const addressData: OrderAddressProps = {
    address: 'Google Building 43, 43 Amphitheatre Pkwy, Mountain View, CA 94043, USA',
    lat: 11.5727473,
    lng: 104.9051283,
  };

  const dateData: OrderDateProps = {
    date: '24 July 2025. 9:00 AM',
    duration: 4,
  };

  return <OrderDetail profileData={profileData} addressData={addressData} dateData={dateData} />;
};

export default ParentComponent;
