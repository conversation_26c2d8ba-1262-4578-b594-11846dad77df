import { PrinterIcon } from "hugeicons-react";
import { Badge } from "../ui/badge";
import IconAssets from "@/asset/icons/icon-assets";

type Props = {
    orderId: string;
    status: string;
}

export default function OrderHeader({ orderId, status }: Props) {
    return (
        <div className="flex items-center justify-between px-4 py-3 bg-white">
            {/* Left section */}
            <div className="flex items-center space-x-4">
                <h2 className="text-lg font-bold">
                    Order ID <span className="font-bold">{orderId}</span>
                </h2>

                <div className="h-5 w-px bg-gray-300" />

                <span className="text-sm font-bold text-[#F6B024] bg-yellow-100 px-3 py-1 rounded-full">
                    {status}
                </span>
            </div>

            {/* Right section */}
            <div className="flex items-center space-x-3">
                <button className="flex items-center gap-1 rounded-md border px-3 py-1.5 text-sm font-medium text-gray-800 hover:bg-gray-100">
                    <PrinterIcon size={24}
                        color="#000000"
                        strokeWidth={1.5} className="w-4 h-4 " />
                    Print
                </button>

                <Badge className="flex items-center gap-1 rounded-md bg-primary px-4 py-1.5 text-sm font-medium text-white hover:bg-primary">
                    <IconAssets.Tick />
                    Pick Up
                </Badge>
            </div>
        </div>
    );
}
