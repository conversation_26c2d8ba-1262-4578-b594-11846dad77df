import { type RouteConfig, index, layout, route } from '@react-router/dev/routes';

export default [
  layout('./protected-layout.tsx', [
    layout('routes/dashboard-layout.tsx', [
      index('routes/dashboard.tsx'),

      // services group
      route('category', 'routes/category/category.tsx'),
      route('category/:id', 'routes/category/new-category.tsx'),
      route('category-addon', 'routes/category-addon/category-addon.tsx'),
      route('category-addon/:id', 'routes/category-addon/new-category-addon.tsx'),
      route('product', 'routes/product/product.tsx'),
      route('product/:id', 'routes/product/new-product.tsx'),
      route('product-option', 'routes/product-option/product-option.tsx'),
      route('product-option/:id', 'routes/product-option/new-product-option.tsx'),

      // ## Marketing Group ##
      route('customer', 'routes/customer.tsx'),
      route('service-bundle', 'routes/service-bundle.tsx'),
      route('top-up', 'routes/top-up.tsx'),
      // Voucher routes
      route('voucher', 'routes/voucher/voucher.tsx'),
      route('voucher/new-voucher', 'routes/voucher/new-voucher.tsx'),
      route('voucher/:voucherId', 'routes/voucher/edit-voucher.tsx'),

      route('promotions', 'routes/promotions.tsx'),
      route('referral-program', 'routes/referral-program.tsx'),
      route('push-notification', 'routes/push-notification.tsx'),
      route('banner', 'routes/banner.tsx'),

      route('finance', 'routes/finance.tsx'),

      // setup
      route('users', 'routes/users.tsx'),
      route('roles', 'routes/roles.tsx')
    ]),
    layout('routes/order/layout.tsx', [route('order', 'routes/order/order.tsx')])
  ]),
  route('login', 'routes/auth/login.tsx')
] satisfies RouteConfig;
